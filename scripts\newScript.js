// 热身练习：获取最新AI回复并进行正则替换
(function () {
  try {
    // 获取最新一层AI回复的消息
    const latestAIMessage = getChatMessages(-1, { role: 'assistant' });

    if (latestAIMessage.length === 0) {
      console.log('没有找到AI回复的消息');
      return;
    }

    const message = latestAIMessage[0];
    console.log('获取到的最新AI消息:', message);
    console.log('消息内容:', message.message);

    // 定义正则表达式（来自regex/查找.md）
    const searchRegex =
      /[\s\S]*<Commissions>\s*([\s\S]*?)\s*<\/Commissions>[\s\S]*<PrivateMessages>\s*([\s\S]*?)\s*<\/PrivateMessages>[\s\S]*/;

    // 定义替换内容（来自regex/替换为.md）
    const replacement = `<sugar_app>
<home>
[commissions_feed]
$1
[/commissions_feed]

[featured_profiles]
[昵称:Yumi酱|简介:大学刚毕业，也是刚注册这个软件。大叔勿扰哦~❤|所在地区:东京, 涩谷|是否认证:已认证|最后在线:在线]
[昵称:甜心樱桃|简介:18岁JK制服爱好者，可以满足你对学生的所有幻想❤第一次尝试援助，请温柔对待|所在地区:东京, 池袋|是否认证:进行中|最后在线:在线]
[昵称:熟女静香|简介:人妻兼职，丈夫不在家的时候可以约。擅长料理和"服务"，体验过的都说好。|所在地区:千叶|是否认证:已认证|最后在线:30分钟前]
[昵称:富豪勋爵|简介:地产集团CEO，寻找能陪我出席社交场合的高质量女伴。慷慨大方。|所在地区:东京, 六本木|是否认证:已认证|最后在线:5分钟前]
[昵称:医学博士|简介:三十五岁外科医生，工作压力大需要放松。喜欢年轻活力的女孩子。|所在地区:横滨|是否认证:已认证|最后在线:刚刚]
[/featured_profiles]

[trending_topics]
[话题标题:# 第一次援交紧张怎么办|参与人数:278|热度指数:956]
[话题标题:# 酒店选择攻略—商务还是情趣|参与人数:165|热度指数:789]
[话题标题:# 如何判断金主爸爸是否大方|参与人数:322|热度指数:1204]
[话题标题:# 情趣用品初次体验感受分享|参与人数:198|热度指数:867]
[话题标题:# 被包养是种怎样的体验|参与人数:256|热度指数:1103]
[/trending_topics]
</home>

<pm>
[昵称:getvar('stat_data.最后聊天对象[0]')|消息:getvar('stat_data.最后聊天对象[0]')|未读消息:0|上次回复:刚刚]
$2
</pm>

<profile>
[昵称:癸|性别:男|身份组:SVIP]
</profile>
</sugar_app>`;

    // 检查消息是否匹配正则表达式
    const match = message.message.match(searchRegex);

    if (match) {
      console.log('找到匹配的内容');
      console.log('匹配组1 (Commissions):', match[1]);
      console.log('匹配组2 (PrivateMessages):', match[2]);

      // 执行替换
      const newMessage = message.message.replace(searchRegex, replacement);
      console.log('替换后的消息:', newMessage);

      // 更新消息内容
      setChatMessages([
        {
          message_id: message.message_id,
          message: newMessage,
        },
      ])
        .then(() => {
          console.log('消息已成功更新');
        })
        .catch(error => {
          console.error('更新消息时出错:', error);
        });
    } else {
      console.log('消息内容不匹配指定的正则表达式模式');
      console.log('当前消息内容:', message.message);
    }
  } catch (error) {
    console.error('执行过程中出现错误:', error);
  }
})();
