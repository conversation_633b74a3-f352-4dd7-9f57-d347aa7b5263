hoistPattern:
  - '*'
hoistedDependencies:
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@cspotcode/source-map-support@0.8.1':
    '@cspotcode/source-map-support': private
  '@discoveryjs/json-ext@0.6.3':
    '@discoveryjs/json-ext': private
  '@jonkemp/package-utils@1.0.8':
    '@jonkemp/package-utils': private
  '@jridgewell/gen-mapping@0.3.12':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/source-map@0.3.10':
    '@jridgewell/source-map': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.29':
    '@jridgewell/trace-mapping': private
  '@parcel/watcher-win32-x64@2.5.1':
    '@parcel/watcher-win32-x64': private
  '@parcel/watcher@2.5.1':
    '@parcel/watcher': private
  '@socket.io/component-emitter@3.1.2':
    '@socket.io/component-emitter': private
  '@tsconfig/node10@1.0.11':
    '@tsconfig/node10': private
  '@tsconfig/node12@1.0.11':
    '@tsconfig/node12': private
  '@tsconfig/node14@1.0.3':
    '@tsconfig/node14': private
  '@tsconfig/node16@1.0.4':
    '@tsconfig/node16': private
  '@types/cors@2.8.19':
    '@types/cors': private
  '@types/eslint-scope@3.7.7':
    '@types/eslint-scope': private
  '@types/eslint@9.6.1':
    '@types/eslint': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/html-minifier-terser@6.1.0':
    '@types/html-minifier-terser': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/node@24.0.10':
    '@types/node': private
  '@types/q@1.5.8':
    '@types/q': private
  '@types/sizzle@2.3.9':
    '@types/sizzle': private
  '@webassemblyjs/ast@1.14.1':
    '@webassemblyjs/ast': private
  '@webassemblyjs/floating-point-hex-parser@1.13.2':
    '@webassemblyjs/floating-point-hex-parser': private
  '@webassemblyjs/helper-api-error@1.13.2':
    '@webassemblyjs/helper-api-error': private
  '@webassemblyjs/helper-buffer@1.14.1':
    '@webassemblyjs/helper-buffer': private
  '@webassemblyjs/helper-numbers@1.13.2':
    '@webassemblyjs/helper-numbers': private
  '@webassemblyjs/helper-wasm-bytecode@1.13.2':
    '@webassemblyjs/helper-wasm-bytecode': private
  '@webassemblyjs/helper-wasm-section@1.14.1':
    '@webassemblyjs/helper-wasm-section': private
  '@webassemblyjs/ieee754@1.13.2':
    '@webassemblyjs/ieee754': private
  '@webassemblyjs/leb128@1.13.2':
    '@webassemblyjs/leb128': private
  '@webassemblyjs/utf8@1.13.2':
    '@webassemblyjs/utf8': private
  '@webassemblyjs/wasm-edit@1.14.1':
    '@webassemblyjs/wasm-edit': private
  '@webassemblyjs/wasm-gen@1.14.1':
    '@webassemblyjs/wasm-gen': private
  '@webassemblyjs/wasm-opt@1.14.1':
    '@webassemblyjs/wasm-opt': private
  '@webassemblyjs/wasm-parser@1.14.1':
    '@webassemblyjs/wasm-parser': private
  '@webassemblyjs/wast-printer@1.14.1':
    '@webassemblyjs/wast-printer': private
  '@webpack-cli/configtest@3.0.1(webpack-cli@6.0.1)(webpack@5.99.9)':
    '@webpack-cli/configtest': private
  '@webpack-cli/info@3.0.1(webpack-cli@6.0.1)(webpack@5.99.9)':
    '@webpack-cli/info': private
  '@webpack-cli/serve@3.0.1(webpack-cli@6.0.1)(webpack@5.99.9)':
    '@webpack-cli/serve': private
  '@xtuc/ieee754@1.2.0':
    '@xtuc/ieee754': private
  '@xtuc/long@4.2.2':
    '@xtuc/long': private
  accepts@1.3.8:
    accepts: private
  acorn-walk@8.3.4:
    acorn-walk: private
  acorn@8.15.0:
    acorn: private
  ajv-formats@2.1.1(ajv@8.17.1):
    ajv-formats: private
  ajv-keywords@3.5.2(ajv@6.12.6):
    ajv-keywords: private
  ajv@6.12.6:
    ajv: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  anymatch@3.1.3:
    anymatch: private
  arg@4.1.3:
    arg: private
  argparse@1.0.10:
    argparse: private
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: private
  array.prototype.reduce@1.0.8:
    array.prototype.reduce: private
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: private
  async-function@1.0.0:
    async-function: private
  asynckit@0.4.0:
    asynckit: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  axios@0.19.2:
    axios: private
  base64id@2.0.0:
    base64id: private
  batch@0.6.1:
    batch: private
  big.js@5.2.2:
    big.js: private
  binary-extensions@2.3.0:
    binary-extensions: private
  boolbase@1.0.0:
    boolbase: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.1:
    browserslist: private
  buffer-from@1.1.2:
    buffer-from: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  camel-case@4.1.2:
    camel-case: private
  caniuse-lite@1.0.30001726:
    caniuse-lite: private
  chalk@4.1.2:
    chalk: private
  cheerio-select@2.1.0:
    cheerio-select: private
  cheerio@1.1.0:
    cheerio: private
  chokidar@3.6.0:
    chokidar: private
  chrome-trace-event@1.0.4:
    chrome-trace-event: private
  clean-css@5.3.3:
    clean-css: private
  cliui@8.0.1:
    cliui: private
  clone-deep@4.0.1:
    clone-deep: private
  coa@2.0.2:
    coa: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  colorette@2.0.20:
    colorette: private
  combined-stream@1.0.8:
    combined-stream: private
  commander@12.1.0:
    commander: private
  cookie@0.7.2:
    cookie: private
  cors@2.8.5:
    cors: private
  cosmiconfig@9.0.0(typescript@5.8.3):
    cosmiconfig: private
  create-require@1.1.1:
    create-require: private
  cross-spawn@7.0.6:
    cross-spawn: private
  css-rules@1.1.0:
    css-rules: private
  css-select-base-adapter@0.1.1:
    css-select-base-adapter: private
  css-select@2.1.0:
    css-select: private
  css-tree@1.0.0-alpha.37:
    css-tree: private
  css-what@6.2.2:
    css-what: private
  cssesc@3.0.0:
    cssesc: private
  csso@4.2.0:
    csso: private
  cssom@0.5.0:
    cssom: private
  data-view-buffer@1.0.2:
    data-view-buffer: private
  data-view-byte-length@1.0.2:
    data-view-byte-length: private
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: private
  debug@4.3.7:
    debug: private
  define-data-property@1.1.4:
    define-data-property: private
  define-properties@1.2.1:
    define-properties: private
  delayed-stream@1.0.0:
    delayed-stream: private
  dependency-graph@1.0.0:
    dependency-graph: private
  detect-libc@1.0.3:
    detect-libc: private
  diff@4.0.2:
    diff: private
  dom-converter@0.2.0:
    dom-converter: private
  dom-serializer@2.0.0:
    dom-serializer: private
  domelementtype@2.3.0:
    domelementtype: private
  domhandler@5.0.3:
    domhandler: private
  domutils@3.2.2:
    domutils: private
  dot-case@3.0.4:
    dot-case: private
  dunder-proto@1.0.1:
    dunder-proto: private
  electron-to-chromium@1.5.179:
    electron-to-chromium: private
  emoji-regex@8.0.0:
    emoji-regex: private
  emojis-list@3.0.0:
    emojis-list: private
  encoding-sniffer@0.2.1:
    encoding-sniffer: private
  engine.io-parser@5.2.3:
    engine.io-parser: private
  engine.io@6.6.4:
    engine.io: private
  enhanced-resolve@5.18.2:
    enhanced-resolve: private
  entities@4.5.0:
    entities: private
  env-paths@2.2.1:
    env-paths: private
  envinfo@7.14.0:
    envinfo: private
  error-ex@1.3.2:
    error-ex: private
  es-abstract@1.24.0:
    es-abstract: private
  es-array-method-boxes-properly@1.0.0:
    es-array-method-boxes-properly: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-module-lexer@1.7.0:
    es-module-lexer: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es-to-primitive@1.3.0:
    es-to-primitive: private
  escalade@3.2.0:
    escalade: private
  escape-string-regexp@1.0.5:
    escape-string-regexp: private
  eslint-scope@5.1.1:
    eslint-scope: private
  esprima@4.0.1:
    esprima: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@4.3.0:
    estraverse: private
  events@3.3.0:
    events: private
  extract-css@2.0.1:
    extract-css: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-uri@3.0.6:
    fast-uri: private
  fastest-levenshtein@1.0.16:
    fastest-levenshtein: private
  fdir@6.4.6(picomatch@4.0.2):
    fdir: private
  fill-range@7.1.1:
    fill-range: private
  find-up@4.1.0:
    find-up: private
  flat-util@1.1.11:
    flat-util: private
  flat@5.0.2:
    flat: private
  follow-redirects@1.5.10:
    follow-redirects: private
  for-each@0.3.5:
    for-each: private
  form-data@4.0.3:
    form-data: private
  fraction.js@4.3.7:
    fraction.js: private
  fs-extra@11.3.0:
    fs-extra: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.8:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  get-symbol-description@1.1.0:
    get-symbol-description: private
  glob-parent@5.1.2:
    glob-parent: private
  glob-to-regexp@0.4.1:
    glob-to-regexp: private
  globalthis@1.0.4:
    globalthis: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  has-bigints@1.1.0:
    has-bigints: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.2.0:
    has-proto: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  he@1.2.0:
    he: private
  href-content@2.0.3:
    href-content: private
  html-minifier-terser@7.2.0:
    html-minifier-terser: private
  htmlparser2@10.0.0:
    htmlparser2: private
  iconv-lite@0.6.3:
    iconv-lite: private
  icss-utils@5.1.0(postcss@8.5.6):
    icss-utils: private
  immutable@5.1.3:
    immutable: private
  import-fresh@3.3.1:
    import-fresh: private
  import-local@3.2.0:
    import-local: private
  inherits@2.0.4:
    inherits: private
  inline-css@3.0.0:
    inline-css: private
  internal-slot@1.1.0:
    internal-slot: private
  interpret@3.1.1:
    interpret: private
  is-array-buffer@3.0.5:
    is-array-buffer: private
  is-arrayish@0.2.1:
    is-arrayish: private
  is-async-function@2.1.1:
    is-async-function: private
  is-bigint@1.1.0:
    is-bigint: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-boolean-object@1.2.2:
    is-boolean-object: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.16.1:
    is-core-module: private
  is-data-view@1.0.2:
    is-data-view: private
  is-date-object@1.1.0:
    is-date-object: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-map@2.0.3:
    is-map: private
  is-negative-zero@2.0.3:
    is-negative-zero: private
  is-number-object@1.1.1:
    is-number-object: private
  is-number@7.0.0:
    is-number: private
  is-plain-object@2.0.4:
    is-plain-object: private
  is-regex@1.2.1:
    is-regex: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: private
  is-string@1.1.1:
    is-string: private
  is-symbol@1.1.1:
    is-symbol: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.1.1:
    is-weakref: private
  is-weakset@2.0.4:
    is-weakset: private
  isarray@2.0.5:
    isarray: private
  isexe@2.0.0:
    isexe: private
  isobject@3.0.1:
    isobject: private
  jest-worker@27.5.1:
    jest-worker: private
  jiti@1.21.7:
    jiti: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json5@1.0.2:
    json5: private
  jsonfile@6.1.0:
    jsonfile: private
  kind-of@6.0.3:
    kind-of: private
  lilconfig@3.1.3:
    lilconfig: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  list-stylesheets@1.2.10:
    list-stylesheets: private
  loader-runner@4.3.0:
    loader-runner: private
  loader-utils@1.4.2:
    loader-utils: private
  locate-path@5.0.0:
    locate-path: private
  lodash.assignin@4.2.0:
    lodash.assignin: private
  lodash.bind@4.2.1:
    lodash.bind: private
  lodash.defaults@4.2.0:
    lodash.defaults: private
  lodash.filter@4.6.0:
    lodash.filter: private
  lodash.flatten@4.4.0:
    lodash.flatten: private
  lodash.foreach@4.5.0:
    lodash.foreach: private
  lodash.map@4.6.0:
    lodash.map: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.pick@4.4.0:
    lodash.pick: private
  lodash.reduce@4.6.0:
    lodash.reduce: private
  lodash.reject@4.6.0:
    lodash.reject: private
  lodash.some@4.6.0:
    lodash.some: private
  lodash@4.17.21:
    lodash: private
  lower-case@2.0.2:
    lower-case: private
  make-error@1.3.6:
    make-error: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  mdn-data@2.0.4:
    mdn-data: private
  mediaquery-text@1.2.0:
    mediaquery-text: private
  merge-stream@2.0.0:
    merge-stream: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  minimist@1.2.8:
    minimist: private
  mkdirp@0.5.6:
    mkdirp: private
  ms@2.1.3:
    ms: private
  nanoid@3.3.11:
    nanoid: private
  negotiator@0.6.3:
    negotiator: private
  neo-async@2.6.2:
    neo-async: private
  no-case@3.0.4:
    no-case: private
  node-addon-api@7.1.1:
    node-addon-api: private
  node-releases@2.0.19:
    node-releases: private
  normalize-path@3.0.0:
    normalize-path: private
  normalize-range@0.1.2:
    normalize-range: private
  nth-check@1.0.2:
    nth-check: private
  object-assign@4.1.1:
    object-assign: private
  object-inspect@1.13.4:
    object-inspect: private
  object-keys@1.1.1:
    object-keys: private
  object.assign@4.1.7:
    object.assign: private
  object.getownpropertydescriptors@2.1.8:
    object.getownpropertydescriptors: private
  object.values@1.2.1:
    object.values: private
  own-keys@1.0.1:
    own-keys: private
  p-limit@2.3.0:
    p-limit: private
  p-locate@4.1.0:
    p-locate: private
  p-try@2.2.0:
    p-try: private
  param-case@3.0.4:
    param-case: private
  parent-module@1.0.1:
    parent-module: private
  parse-json@5.2.0:
    parse-json: private
  parse5-htmlparser2-tree-adapter@7.1.0:
    parse5-htmlparser2-tree-adapter: private
  parse5-parser-stream@7.1.2:
    parse5-parser-stream: private
  parse5@7.3.0:
    parse5: private
  pascal-case@3.1.2:
    pascal-case: private
  path-exists@4.0.0:
    path-exists: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  pick-util@1.1.5:
    pick-util: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  pify@2.3.0:
    pify: private
  pkg-dir@4.2.0:
    pkg-dir: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postcss-load-config@5.1.0(jiti@1.21.7)(postcss@8.5.6):
    postcss-load-config: private
  postcss-modules-extract-imports@3.1.0(postcss@8.5.6):
    postcss-modules-extract-imports: private
  postcss-modules-local-by-default@4.2.0(postcss@8.5.6):
    postcss-modules-local-by-default: private
  postcss-modules-scope@3.2.1(postcss@8.5.6):
    postcss-modules-scope: private
  postcss-modules-values@4.0.0(postcss@8.5.6):
    postcss-modules-values: private
  postcss-reporter@7.1.0(postcss@8.5.6):
    postcss-reporter: private
  postcss-selector-parser@7.1.0:
    postcss-selector-parser: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  pretty-error@4.0.0:
    pretty-error: private
  pretty-hrtime@1.0.3:
    pretty-hrtime: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  punycode@2.3.1:
    punycode: private
  q@1.5.1:
    q: private
  randombytes@2.1.0:
    randombytes: private
  read-cache@1.0.0:
    read-cache: private
  readable-stream@3.6.2:
    readable-stream: private
  readdirp@3.6.0:
    readdirp: private
  rechoir@0.8.0:
    rechoir: private
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: private
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: private
  relateurl@0.2.7:
    relateurl: private
  remote-content@4.0.1:
    remote-content: private
  renderkid@3.0.0:
    renderkid: private
  require-directory@2.1.1:
    require-directory: private
  require-from-string@2.0.2:
    require-from-string: private
  resolve-cwd@3.0.0:
    resolve-cwd: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve@1.22.10:
    resolve: private
  safe-array-concat@1.1.3:
    safe-array-concat: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-push-apply@1.0.0:
    safe-push-apply: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safer-buffer@2.1.2:
    safer-buffer: private
  sax@1.2.4:
    sax: private
  schema-utils@2.7.1:
    schema-utils: private
  semver@7.7.2:
    semver: private
  serialize-javascript@6.0.2:
    serialize-javascript: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  set-proto@1.0.0:
    set-proto: private
  shallow-clone@3.0.1:
    shallow-clone: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  slash@5.1.0:
    slash: private
  slick@1.12.2:
    slick: private
  socket.io-adapter@2.5.5:
    socket.io-adapter: private
  socket.io-parser@4.2.4:
    socket.io-parser: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.7.4:
    source-map: private
  specificity@0.4.1:
    specificity: private
  sprintf-js@1.0.3:
    sprintf-js: private
  stable@0.1.8:
    stable: private
  stop-iteration-iterator@1.1.0:
    stop-iteration-iterator: private
  string-width@4.2.3:
    string-width: private
  string.prototype.trim@1.2.10:
    string.prototype.trim: private
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  string_decoder@1.3.0:
    string_decoder: private
  strip-ansi@6.0.1:
    strip-ansi: private
  strip-bom@3.0.0:
    strip-bom: private
  style-data@1.4.8:
    style-data: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  svgo@1.3.2:
    svgo: private
  tapable@2.2.2:
    tapable: private
  terser@5.43.1:
    terser: private
  thenby@1.3.4:
    thenby: private
  tinyglobby@0.2.14:
    tinyglobby: private
  to-regex-range@5.0.1:
    to-regex-range: private
  tsconfig-paths@4.2.0:
    tsconfig-paths: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: private
  typed-array-length@1.0.7:
    typed-array-length: private
  unbox-primitive@1.1.0:
    unbox-primitive: private
  undici-types@7.8.0:
    undici-types: private
  undici@7.11.0:
    undici: private
  universalify@2.0.1:
    universalify: private
  unquote@1.1.1:
    unquote: private
  update-browserslist-db@1.1.3(browserslist@4.25.1):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  util-deprecate@1.0.2:
    util-deprecate: private
  util.promisify@1.0.1:
    util.promisify: private
  utila@0.4.0:
    utila: private
  v8-compile-cache-lib@3.0.1:
    v8-compile-cache-lib: private
  vary@1.1.2:
    vary: private
  watchpack@2.4.4:
    watchpack: private
  webpack-merge@6.0.1:
    webpack-merge: private
  webpack-sources@3.3.3:
    webpack-sources: private
  whatwg-encoding@3.1.1:
    whatwg-encoding: private
  whatwg-mimetype@4.0.0:
    whatwg-mimetype: private
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: private
  which-builtin-type@1.2.1:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@2.0.2:
    which: private
  wildcard@2.0.1:
    wildcard: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
  ws@8.17.1:
    ws: private
  y18n@5.0.8:
    y18n: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  yn@3.1.1:
    yn: private
ignoredBuilds: []
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.4
pendingBuilds: []
prunedAt: Thu, 03 Jul 2025 09:21:00 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@parcel/watcher-android-arm64@2.5.1'
  - '@parcel/watcher-darwin-arm64@2.5.1'
  - '@parcel/watcher-darwin-x64@2.5.1'
  - '@parcel/watcher-freebsd-x64@2.5.1'
  - '@parcel/watcher-linux-arm-glibc@2.5.1'
  - '@parcel/watcher-linux-arm-musl@2.5.1'
  - '@parcel/watcher-linux-arm64-glibc@2.5.1'
  - '@parcel/watcher-linux-arm64-musl@2.5.1'
  - '@parcel/watcher-linux-x64-glibc@2.5.1'
  - '@parcel/watcher-linux-x64-musl@2.5.1'
  - '@parcel/watcher-win32-arm64@2.5.1'
  - '@parcel/watcher-win32-ia32@2.5.1'
  - fsevents@2.3.3
storeDir: E:\.pnpm-store\v10
virtualStoreDir: E:\XiGPrograms\sillyTavern\myContents\STdev\UI_Editing\tavern_helper_template\node_modules\.pnpm
virtualStoreDirMaxLength: 60
