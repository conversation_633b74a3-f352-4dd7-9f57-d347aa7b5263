/? (slash|macros|format|hotkeys)? // Get help on macros, chat formatting and commands.
// (string)? // Write a comment.
/abort [quiet=true|false]?=true (string)? // Aborts the slash command batch execution.
/abs (number|varname) // Performs an absolute value operation of a value and passes the result down the pipe. Can use variable names. Example: /abs i
/add (...number|varname|list) // Performs an addition of the set of values and passes the result down the pipe. Can use variable names, or a JSON array consisting of numbers and variables (with quotes). Example: /add 10 i 30 j /add ["count", 15, 2, "i"]
/addglobalvar [key=varname] (number|string) // Add a value to a global variable and pass the result down the pipe. Example: /addglobalvar key=score 10
/addswipe [switch=true|false]? (string) // Adds a swipe to the last chat message. Use switch=true to switch to directly switch to the new swipe.
/addvar [key=varname] (number|string) // Add a value to a local variable and pass the result down the pipe. Example: /addvar key=score 10
/api [quiet=true|false]?=false (kobold|horde|novel|koboldcpp|kcpp|openai|oai|google|openrouter|openrouter-text|ooba|mancer|vllm|aphrodite|tabby|togetherai|llamacpp|ollama|infermaticai|dreamgen|featherless|huggingface|generic|claude|ai21|makersuite|vertexai|mistralai|custom|cohere|perplexity|groq|01ai|nanogpt|deepseek|aimlapi|xai|pollinations)? // Connect to an API. If no argument is provided, it will return the currently connected API. Available APIs: 01ai, ai21, aimlapi, aphrodite, claude, cohere, custom, deepseek, dreamgen, featherless, generic, google, groq, horde, huggingface, infermaticai, kcpp, kobold, koboldcpp, llamacpp, makersuite, mancer, mistralai, nanogpt, novel, oai, ollama, ooba, openai, openrouter, openrouter-text, perplexity, pollinations, tabby, togetherai, vertexai, vllm, xai
/api-url [api=custom|kobold|ooba|mancer|vllm|aphrodite|tabby|koboldcpp|togetherai|llamacpp|ollama|infermaticai|dreamgen|openrouter|featherless|huggingface|generic]? [connect=true|false]?=true [quiet=true|false]?=false (string)? // Set the API url / server url for the currently selected API, including the port. If no argument is provided, it will return the current API url. If a manual API is provided to set the URL, make sure to set connect=false, as auto-connect only works for the currently selected API, or consider switching to it with /api first. This slash command works for most of the Text Completion sources, KoboldAI Classic, and also Custom OpenAI compatible for the Chat Completion sources. If unsure which APIs are supported, check the auto-completion of the optional api argument of this command.
/ask [name=string] [return=pipe|object|toast-html|toast-text|console|none]?=pipe (string)? // Asks a specified character card a prompt. Character name must be provided in a named argument.
/audioenable [type=bgm|ambient] [state=true|false]?=true // 控制音乐播放器或音效播放器的开启与关闭。 Example: /audioenable type=bgm state=true 打开音乐播放器。 /audioenable type=ambient state=false 关闭音效播放器。
/audioimport [type=bgm|ambient] [play=true|false]?=true (string) // 导入音频或音乐链接，并决定是否立即播放，默认为自动播放。可批量导入链接，使用英文逗号分隔。 Example: /audioimport type=bgm https://example.com/song1.mp3,https://example.com/song2.mp3 导入 BGM 音乐并立即播放第一个链接。 /audioimport type=ambient play=false url=https://example.com/sound1.mp3,https://example.com/sound2.mp3 导入音效链接 (不自动播放)。
/audiomode [type=bgm|ambient] [mode=repeat|random|single|stop] // 设置音频播放模式。 Example: /audiomode type=bgm mode=repeat 设置音乐为循环播放模式。 /audiomode type=ambient mode=random 设置音效为随机播放模式。 /audiomode type=bgm mode=single 设置音乐为单曲循环模式。 /audiomode type=ambient mode=stop 设置音效为停止播放模式。
/audioplay [type=bgm|ambient] [play=true|false]=true // 控制音乐播放器或音效播放器的播放与暂停。 Example: /audioplay type=bgm 播放当前音乐。 /audioplay type=ambient play=false 暂停当前音效。
/audioselect [type=bgm|ambient] (string) // 选择并播放音频。如果音频链接不存在，则先导入再播放。 Example: /audioselect type=bgm https://example.com/song.mp3 选择并播放指定的音乐。 /audioselect type=ambient https://example.com/sound.mp3 选择并播放指定的音效。
/autobg // Automatically changes the background based on the chat context using the AI request prompt
/bg (string)? // Sets a background according to the provided filename. Partial names allowed. If no background is provided, this will return the currently selected background. Example: /bg beach.jpg /bg
/bgcol // – WIP test of auto-bg avg coloring
/branch-create (number)? // Create a new branch from the selected message. If no message id is provided, will use the last message. Creating a branch will automatically choose a name for the branch. After creating the branch, the branch chat will be automatically opened. Use Checkpoints and /checkpoint-create instead if you do not want to jump to the new chat.
/break (string|number|range|bool|varname|closure|subcommand|list|dictionary)? // Break out of a loop or closure executed through /run or /:
/breakpoint // Set a breakpoint for debugging in the QR Editor.
/bubble // Sets the message style to bubble chat mode.
/buttons [labels=list] [multiple=true|false]?=false (string) // Shows a blocking popup with the specified text and buttons. Returns the clicked button label into the pipe or empty string if canceled. Example: /buttons labels=["Yes","No"] Do you want to continue?
/caption [quiet=true|false]?=false [mesId=number]? (string)? // Caption an image with an optional prompt and passes the caption down the pipe. Only multimodal sources support custom prompts. Provide a message ID to get an image from a message instead of uploading one. Set the "quiet" argument to true to suppress sending a captioned message, default: false.
/char-find [...tag=string]? [preferCurrent=true|false]?=true [quiet=true|false]?=false (string)? // Searches for a character and returns its avatar key. This can be used to choose the correct character for something like /sendas or other commands in need of a character name if you have multiple characters with the same name. Example: /char-find name="Chloe" Returns the avatar key for "Chloe". /search name="Chloe" tag="friend" Returns the avatar key for the character "Chloe" that is tagged with "friend". This is useful if you for example have multiple characters named "Chloe", and the others are "foe", "goddess", or anything else, so you can actually select the character you are looking for.
/chat-jump (number) // Scrolls the chat view to the specified message index. Index starts at 0. Example: /chat-jump 10 Scrolls to the 11th message (id=10).
/chat-manager // Opens the chat manager for the current character/group.
/chat-reload // Reloads the current chat.
/chat-render [scroll=true|false]?=false (number)? // Renders a specified number of messages into the chat window. Displays all messages if no argument is provided.
/checkpoint-create [mesId=number]? (string)? // Create a new checkpoint for the selected message with the provided name. If no message id is provided, will use the last message. Leave the checkpoint name empty to auto-generate one. A created checkpoint will be permanently linked with the message. If a checkpoint already exists, the link to it will be overwritten. After creating the checkpoint, the checkpoint chat can be opened with the checkpoint flag, using the /go command with the checkpoint name or the /checkpoint-go command on the message. Use Branches and /branch-create instead if you do want to jump to the new chat. Example: /checkpoint-create mes={{lastCharMessage}} Checkpoint for char reply | /setvar key=rememberCheckpoint {{pipe}} Will create a new checkpoint to the latest message of the current character, and save it as a local variable for future use.
/checkpoint-exit // Exit the checkpoint chat.If not in a checkpoint chat, returns empty string.
/checkpoint-get (number)? // Get the name of the checkpoint linked to the selected message. If no message id is provided, will use the last message. If no checkpoint is linked, the result will be empty.
/checkpoint-go (number)? // Open the checkpoint linked to the selected message. If no message id is provided, will use the last message. Use /checkpoint-get if you want to make sure that the selected message has a checkpoint.
/checkpoint-list [links=true|false]?=false // List all existing checkpoints in this chat. Returns a list of all message ids that have a checkpoint, or all checkpoint links if links is set to true. The value will be a JSON array.
/checkpoint-parent // Get the name of the parent chat for this checkpoint.If not in a checkpoint chat, returns empty string.
/clipboard-get // Retrieves the text from the OS clipboard. Only works in secure contexts (HTTPS or localhost). Browser may ask for permission.
/clipboard-set (string) // Copies the provided text to the OS clipboard. Returns an empty string.
/closechat // Closes the current chat.
/closure-deserialize (string) // Deserialize a closure from text. Examples: /closure-deserialize {{getvar::myClosure}} | /let myClosure {{pipe}} | /let y bar | /:myClosure x=foo
/closure-serialize (closure) // Serialize a closure as text that can be stored in global and chat variables. Examples: /closure-serialize {: x=1 /echo x is {{var::x}} and y is {{var::y}} :} | /setvar key=myClosure
/comment [compact=true|false]?=false [at=number]? [return=pipe|object|toast-html|toast-text|console|none]?=none [raw=true|false]?=true (string) // Adds a note/comment message not part of the chat. If compact is set to true, the message is sent using a compact layout. Example: /comment This is a comment /comment compact=true This is a compact comment
/context [quiet=true|false]?=false (string)? // Selects context template by name. Gets the current template if no name is provided
/continue [await=true|false]?=false (string)? // Continues the last message in the chat, with an optional additional prompt. If await=true named argument is passed, the command will await for the continued generation before proceeding. Example: /continue Continues the chat with no additional prompt and immediately proceeds to the next command. /continue await=true Let's explore this further... Continues the chat with the provided prompt and waits for the generation to finish.
/cos (number|varname) // Performs a cosine operation of a value and passes the result down the pipe. Can use variable names. Example: /cos i
/count // Counts the number of tokens in the current chat.
/createentry [file=string] [key=string]? (string)? // Create a new record in the specified book with the key and content (both are optional) and pass the UID down the pipe. Example: /createentry file=chatLore key=Shadowfang The sword of the king
/css-var [varname=string] [to=chat|background|zoomedAvatar|gallery]?=chat (string) // Sets a CSS variable to a specified value on a target element. Only setting of variable names is supported. They have to be prefixed with double dashes ("--exampleVar"). Setting actual CSS properties is not supported. Custom CSS in the theme settings can be used for that. This value will be gone after a page reload! Example: /css-var varname="--SmartThemeBodyColor" #ff0000 Sets the text color of the chat to red /css-var to=zoomedAvatar varname="--SmartThemeBlurStrength" 0 Remove the blur from the zoomed avatar
/cut (...number|range) // Cuts the specified message or continuous chunk from the chat. Ranges are inclusive! Example: /cut 0-10
/db // Open the data bank
/db-add [source=global|character|chat]?=chat [name=string]? (string) // Add an attachment to the Data Bank. If name is not provided, it will be generated automatically. Returns the URL of the attachment.
/db-delete [source=global|character|chat]?=chat (string) // Delete an attachment from the Data Bank.
/db-disable [source=global|character|chat]? (string) // Disable an attachment in the Data Bank by its name or URL. Optionally, provide the source of the attachment.
/db-enable [source=global|character|chat]? (string) // Enable an attachment in the Data Bank by its name or URL. Optionally, provide the source of the attachment.
/db-get [source=global|character|chat]? (string) // Get attachment text from the Data Bank. Either provide the name or URL of the attachment. Optionally, provide the source of the attachment.
/db-ingest // Force the ingestion of all Data Bank attachments.
/db-list [source=global|character|chat]? [field=name|url]?=url // List attachments in the Data Bank as a JSON-serialized array. Optionally, provide the source of the attachments and the field to list by.
/db-purge // Purge the vector index for all Data Bank attachments.
/db-search [threshold=number]? [count=number]? [source=global|character|chat]? [return=chunks|pipe|object|toast-html|toast-text|console|none]?=object (string) // Search the Data Bank for a specific query using vector similarity. Returns a list of file URLs with the most relevant content.
/db-update [source=global|character|chat]?=chat [name=string]? [url=string]? (string) // Update an attachment in the Data Bank, preserving its name. Returns a new URL of the attachment.
/decglobalvar (varname) // Decrement a global variable by 1 and pass the result down the pipe. Example: /decglobalvar score
/decvar (varname) // Decrement a local variable by 1 and pass the result down the pipe. Example: /decvar score
/del (number)? // Enter message deletion mode, and auto-deletes last N messages if numeric argument is provided.
/delay (number) // Delays the next command in the pipe by the specified number of milliseconds. Example: /delay 1000
/delchat // Deletes the current chat.
/delname (string) // Deletes all messages attributed to a specified name. Example: /delname John
/delswipe (number) // Deletes a swipe from the last chat message. If swipe id is not provided, it deletes the current swipe. Example: /delswipe Deletes the current swipe. /delswipe 2 Deletes the second swipe from the last chat message.
/div (number|varname) (number|varname) // Performs a division of two values and passes the result down the pipe. Can use variable names. Example: /div 10 i
/dupe // Duplicates the currently selected character.
/echo [title=string]? [severity=string]?=info [timeout=number]?=4000 [extendedTimeout=number]?=10000 [preventDuplicates=true|false]?=false [awaitDismissal=true|false]?=false [cssClass=string]? [color=string]? [escapeHtml=true|false]?=true [onClick=closure]? [raw=true|false]?=true (string) // Echoes the provided text to a toast message. Can be used to display informational messages or for pipes debugging. Example: /echo title="My Message" severity=warning This is a warning message /echo color=purple This message is purple /echo onClick={: /echo escapeHtml=false color=transparent cssClass=wider_dialogue_popup <img src="/img/five.png" /> :} timeout=5000 Clicking on this message within 5 seconds will open the image.
/ejs [ctx=dictionary]? [block=dictionary]? (string) // Execute template code
/ejs-refresh // Preload world info
/event-emit [event=string] [...data=string]? // 发送 `event` 事件, 同时可以发送一些数据. 所有正在监听该消息频道的 listener 函数都会自动运行, 并能用函数参数接收发送来的数据. 由于酒馆 STScript 输入方式的局限性, 所有数据将会以字符串 string 类型接收; 如果需要 number 等类型, 请自行转换. Example: /event-emit event="读档" /event-emit event="存档" data={{getvar::数据}} data=8 data=你好 {{user}} /event-emit event="随便什么名称" data="这是一个 数据" data={{user}}
/expression-classify [api=local|extras|llm|webllm|none]? [filter=true|false]?=true [prompt=string]? (string) // Performs an emotion classification of the given text and returns a label. Allows to specify which Classifier API to perform the classification with. Example: /classify I am so happy today!
/expression-folder-override (string)? // Sets an override sprite folder for the current character. In groups, this will apply to the character who last sent a message. If the name starts with a slash or a backslash, selects a sub-folder in the character-named folder. Empty value to reset to default.
/expression-last (string)? // Returns the last set expression for the named character.
/expression-list [return=pipe|object|toast-html|toast-text|console|none]?=pipe [filter=true|false]?=true // Returns a list of available expressions, including custom expressions.
/expression-set [type=expression|sprite]?=expression (string) // Force sets the expression for the current character.
/expression-upload [name=string]? [label=string] [folder=string]? [spriteName=string]? (string) // Upload a sprite from a URL. Example: /uploadsprite name=Seraphina label=joy /user/images/Seraphina/<EMAIL>
/extension-disable [reload=true|false]?=true (string) // Disables a specified extension. By default, the page will be reloaded automatically, stopping any further commands. If reload=false named argument is passed, the page will not be reloaded, and the extension will stay enabled until refreshed. The page either needs to be refreshed, or /reload-page has to be called. Example: /extension-disable Summarize
/extension-enable [reload=true|false]?=true (string) // Enables a specified extension. By default, the page will be reloaded automatically, stopping any further commands. If reload=false named argument is passed, the page will not be reloaded, and the extension will stay disabled until refreshed. The page either needs to be refreshed, or /reload-page has to be called. Example: /extension-enable Summarize
/extension-exists (string) // Checks if a specified extension exists. Example: /extension-exists SillyTavern-LALib
/extension-state (string) // Returns the state of a specified extension (true if enabled, false if disabled). Example: /extension-state Summarize
/extension-toggle [reload=true|false]?=true [state=true|false]? (string) // Toggles the state of a specified extension. By default, the page will be reloaded automatically, stopping any further commands. If reload=false named argument is passed, the page will not be reloaded, and the extension will stay in its current state until refreshed. The page either needs to be refreshed, or /reload-page has to be called. Example: /extension-toggle Summarize /extension-toggle Summarize state=true
/findentry [file=string] [field=key|keysecondary|comment|content|constant|vectorized|selective|selectiveLogic|addMemo|order|position|disable|excludeRecursion|preventRecursion|matchPersonaDescription|matchCharacterDescription|matchCharacterPersonality|matchCharacterDepthPrompt|matchScenario|matchCreatorNotes|delayUntilRecursion|probability|useProbability|depth|group|groupOverride|groupWeight|scanDepth|caseSensitive|matchWholeWords|useGroupScoring|automationId|role|sticky|cooldown|delay|characterFilterNames|characterFilterTags|characterFilterExclude|triggers]?=key (...string) // Find a UID of the record from the specified book using the fuzzy match of a field value (default: key) and pass it down the pipe. Example: /findentry file=chatLore field=key Shadowfang
/flat // Sets the message style to flat chat mode.
/flushglobalvar (varname|closure)? // Deletes the specified global variable. Example: /flushglobalvar score Deletes the global variable score.
/flushinject (string)? // Removes a script injection for the current chat. If no ID is provided, removes all script injections.
/flushvar (varname|closure)? // Delete a local variable. Example: /flushvar score
/forcesave // Forces a save of the current chat and settings
/fuzzy [list=list|varname] [threshold=number]?=0.4 [mode=first|best]?=first (string) // Performs a fuzzy match of each item in the list against the text to search. If any item matches, then its name is returned. If no item matches the text, no value is returned. The optional threshold (default is 0.4) allows control over the match strictness. A low value (min 0.0) means the match is very strict. At 1.0 (max) the match is very loose and will match anything. The optional mode argument allows to control the behavior when multiple items match the text. first (default) returns the first match below the threshold. best returns the best match below the threshold. The returned value passes to the next command through the pipe. Example: /fuzzy list=["a","b","c"] threshold=0.4 abc
/gen [lock=on|off]? [name=string]?=System [length=number]? [as=system|char]? (string) // Generates text using the provided prompt and passes it to the next command through the pipe, optionally locking user input while generating and allowing to configure the in-prompt name for instruct mode (default = "System"). "as" argument controls the role of the output prompt: system (default) or char. If "length" argument is provided as a number in tokens, allows to temporarily override an API response length.
/genraw [lock=on|off]?=off [instruct=on|off]?=on [stop=list]?=[] [as=system|char]?=system [system=string|varname]? [prefill=string|varname]? [length=number|varname]? [trim=on|off]?=on (string) // Generates text using the provided prompt and passes it to the next command through the pipe, optionally locking user input while generating. Does not include chat history or character card. Use instruct=off to skip instruct formatting, e.g. /genraw instruct=off Why is the sky blue? Use stop=... with a JSON-serialized array to add one-time custom stop strings, e.g. /genraw stop=["\n"] Say hi "as" argument controls the role of the output prompt: system (default) or char. "system" argument adds an (optional) system prompt at the start. If "length" argument is provided as a number in tokens, allows to temporarily override an API response length.
/getcharbook [type=primary|additional|all]?=primary (number|string)? // Get a name of the character-bound lorebook and pass it down the pipe. Returns empty string if character lorebook is not set. Does not work in group chats without providing a character avatar name.
/getchatbook [name=string]? // Get a name of the chat-bound lorebook or create a new one if was unbound, and pass it down the pipe.
/getchatname // Returns the name of the current chat file into the pipe.
/getentryfield [file=string] [field=key|keysecondary|comment|content|constant|vectorized|selective|selectiveLogic|addMemo|order|position|disable|excludeRecursion|preventRecursion|matchPersonaDescription|matchCharacterDescription|matchCharacterPersonality|matchCharacterDepthPrompt|matchScenario|matchCreatorNotes|delayUntilRecursion|probability|useProbability|depth|group|groupOverride|groupWeight|scanDepth|caseSensitive|matchWholeWords|useGroupScoring|automationId|role|sticky|cooldown|delay|characterFilterNames|characterFilterTags|characterFilterExclude|triggers]?=content (string) // Get a field value (default: content) of the record with the UID from the specified book and pass it down the pipe. Example: /getentryfield file=chatLore field=content 123
/getglobalbooks // Get a list of names of the selected global lorebooks and pass it down the pipe.
/getglobalvar [key=varname]? [index=number|string]? (varname)? // Get a global variable value and pass it down the pipe. The index argument is optional. Examples: /getglobalvar height /getglobalvar key=height /getglobalvar index=3 costumes
/getpersonabook // Get a name of the current persona-bound lorebook and pass it down the pipe. Returns empty string if persona lorebook is not set.
/getpromptentry [...identifier=string|list]? [...name=string|list]? [return=simple|list|dict]?=simple // Gets the state of the specified prompt entries. If return is simple (default) then the return will be a single value if only one value was retrieved; otherwise uses a dict (if the identifier parameter was used) or a list.
/getvar [key=varname]? [index=number|string]? (varname)? // Get a local variable value and pass it down the pipe. The index argument is optional. Examples: /getvar height /getvar key=height /getvar index=3 costumes
/go (string) // Opens up a chat with the character or group by its name
/hide [name=string]? (number|range)? // Hides a chat message from the prompt.
/if [left=varname|string|number] [right=varname|string|number]? [rule=eq|neq|in|nin|gt|gte|lt|lte|not]?=eq [else=closure|subcommand]? (closure|subcommand) // Compares the value of the left operand a with the value of the right operand b, and if the condition yields true, then execute any valid slash command enclosed in quotes and pass the result of the command execution down the pipe. Numeric values and string literals for left and right operands supported. If the rule is not provided, it defaults to eq. If no right operand is provided, it defaults to checking the left value to be truthy. A non-empty string or non-zero number is considered truthy, as is the value true or on. Only acceptable rules for no provided right operand are not, and no provided rule - which default to returning whether it is not or is truthy. Available rules: eq => a == b (strings & numbers) neq => a !== b (strings & numbers) in => a includes b (strings & numbers as strings) nin => a not includes b (strings & numbers as strings) gt => a > b (numbers) gte => a >= b (numbers) lt => a < b (numbers) lte => a <= b (numbers) not => !a (truthy) Examples: /if left=score right=10 rule=gte "/speak You win" triggers a /speak command if the value of "score" is greater or equals 10. /if left={{lastMessage}} rule=in right=surprise {: /echo SURPISE! :} executes a subcommand defined as a closure if the given value contains a specified word. /if left=myContent {: /echo My content had some content. :} executes the defined subcommand, if the provided value of left is truthy (contains some kind of contant that is not empty or false) /if left=tree right={{getvar::object}} {: /echo The object is a tree! :} executes the defined subcommand, if the left and right values are equals.
/imagine [quiet=true|false]?=false [negative=string]? [extend=true|false]? [edit=true|false]? [multimodal=true|false]? [snap=true|false]? [seed=number]? [width=number]? [height=number]? [steps=number]? [cfg=number]? [skip=number]? [model=string]? [sampler=string]? [scheduler=string]? [vae=string]? [upscaler=string]? [hires=true|false]? [scale=number]? [denoise=number]? [2ndpass=number]? [faces=true|false]? (you|me|scene|raw_last|last|face|background)? // Requests to generate an image and posts it to chat (unless quiet=true argument is specified). Supported arguments: you, me, scene, raw_last, last, face, background. Anything else would trigger a "free mode" to make generate whatever you prompted. Example: /imagine apple tree would generate a picture of an apple tree. Returns a link to the generated image.
/imagine-comfy-workflow (string) // (workflowName) - change the workflow to be used for image generation with ComfyUI, e.g. /imagine-comfy-workflow MyWorkflow
/imagine-source (string)? // If an argument is provided, change the source of the image generation, e.g. /imagine-source comfy. Returns the current source.
/imagine-style (string)? // If an argument is provided, change the style of the image generation, e.g. /imagine-style MyStyle. Returns the current style.
/impersonate [await=true|false]?=false (string)? // Calls an impersonation response, with an optional additional prompt. If await=true named argument is passed, the command will wait for the impersonation to end before continuing. Example: /impersonate What is the meaning of life?
/import [from=string] (...string) // Import one or more closures from another Quick Reply. Only imports closures that are directly assigned a scoped variable via /let or /var. Examples: /import from=LibraryQrSet.FooBar foo | /:foo /import from=LibraryQrSet.FooBar foo bar | /:foo | /:bar /import from=LibraryQrSet.FooBar foo as x bar as y | /:x | /:y
/incglobalvar (varname) // Increment a global variable by 1 and pass the result down the pipe. Example: /incglobalvar score
/incvar (varname) // Increment a local variable by 1 and pass the result down the pipe. Example: /incvar score
/inject [id=string]? [position=before|after|chat|none]?=after [depth=number]?=4 [scan=true|false]?=false [role=system|assistant|user]? [ephemeral=true|false]?=false [filter=closure]? (string)? // Injects a text into the LLM prompt for the current chat. Requires a unique injection ID (will be auto-generated if not provided). Positions: "before" main prompt, "after" main prompt, in-"chat", hidden with "none" (default: after). Depth: injection depth for the prompt (default: 4). Role: role for in-chat injections (default: system). Scan: include injection content into World Info scans (default: false). Hidden injects in "none" position are not inserted into the prompt but can be used for triggering WI entries. Returns the injection ID.
/input [default=string]? [large=on|off]?=off [wide=on|off]?=off [okButton=string]?=Ok [rows=number]? [onSuccess=closure]? [onCancel=closure]? (string)? // Shows a popup with the provided text and an input field. The default argument is the default value of the input field, and the text argument is the text to display.
/instruct [quiet=true|false]?=false [forceGet=true|false]?=false (string)? // Selects instruct mode template by name. Enables instruct mode if not already enabled. Gets the current instruct template if no name is provided and instruct mode is enabled or forceGet=true is passed. Example: /instruct creative
/instruct-off // Disables instruct mode
/instruct-on // Enables instruct mode.
/instruct-state (true|false)? // Gets the current instruct mode state. If an argument is provided, it will set the instruct mode state.
/is-mobile // Returns true if the current device is a mobile device, false otherwise. Equivalent to {{isMobile}} macro.
/len (string|number|list|dictionary) // Gets the length of a value and passes the result down the pipe. For strings, returns the number of characters. For lists and dictionaries, returns the number of elements. For numbers, returns the number of digits (including the sign and decimal point). Example: /len Lorem ipsum | /echo
/let [key=varname]? (varname)? (string|number|bool|list|dictionary|closure)? // Declares a new variable in the current scope. Examples: /let x foo bar | /echo {{var::x}} /let key=x foo bar | /echo {{var::x}} /let y
/list-gallery [char=string]? [group=string]? // List images in the gallery of the current char / group or a specified char / group.
/listinjects [return=object|chat-html|popup-html|toast-html|console|none]?=popup-html [format=popup|chat|none] // Lists all script injections for the current chat. Displays injects in a popup by default. Use the return argument to change the return type.
/listvar [scope=all|local|global]?=all [return=object|chat-html|popup-html|toast-html|console|none]?=popup-html [format=popup|chat|none] // List registered chat variables. Displays variables in a popup by default. Use the return argument to change the return type.
/lock [type=chat|character|default]?=chat (string)?=toggle // Locks/unlocks a persona (name and avatar) to the current chat. Gets the current lock state for the given type if no state is provided.
/lockbg // Locks a background for the currently selected chat
/log (number|varname) // Performs a logarithm operation of a value and passes the result down the pipe. Can use variable names. Example: /log i
/lower (string) // Converts the provided string to lowercase.
/match [pattern=string] (string) // Retrieves regular expression matches in the given text Returns an array of groups (with the first group being the full match). If the regex contains the global flag (i.e. /g), multiple nested arrays are returned for each match. If the regex is global, returns [] if no matches are found, otherwise it returns an empty string. Example: /let x color_green green lamp color_blue                                                                            || /match pattern="green" {{var::x}}            | /echo  |/# [ "green" ]                                               || /match pattern="color_(\w+)" {{var::x}}      | /echo  |/# [ "color_green", "green" ]                                || /match pattern="/color_(\w+)/g" {{var::x}}   | /echo  |/# [ [ "color_green", "green" ], [ "color_blue", "blue" ] ]  || /match pattern="orange" {{var::x}}           | /echo  |/#                                                           || /match pattern="/orange/g" {{var::x}}        | /echo  |/# []                                                        ||
/max (...number|varname|list) // Returns the maximum value of the set of values and passes the result down the pipe. Can use variable names, or a JSON array consisting of numbers and variables (with quotes). Examples: /max 10 i 30 j /max ["count", 15, 2, "i"]
/member-add (string) // Adds a new group member to the group chat. Example: /member-add John Doe
/member-count // Returns the total number of group members in the group chat list.
/member-disable (number|string) // Disables a group member from being drafted for replies.
/member-down (number|string) // Moves a group member down in the group chat list.
/member-enable (number|string) // Enables a group member to be drafted for replies.
/member-get [field=name|index|avatar|id]=name (number|string) // Retrieves a group member's name, index, id, or avatar.
/member-peek (number|string) // Shows a group member character card without switching chats. Examples: /peek Gloria Shows the character card for the character named "Gloria".
/member-remove (number|string) // Removes a group member from the group chat. Example: /member-remove 2 /member-remove John Doe
/member-up (number|string) // Moves a group member up in the group chat list.
/messages [names=on|off]?=off [hidden=on|off]?=on [role=system|assistant|user]? (number|range) // Returns the specified message or range of messages as a string. Use the hidden=off argument to exclude hidden messages. Use the role argument to filter messages by role. Possible values are: system, assistant, user. Examples: /messages 10 Returns the 10th message. /messages names=on 5-10 Returns messages 5 through 10 with author names.
/min (...number|varname|list) // Returns the minimum value of the set of values and passes the result down the pipe. Can use variable names, or a JSON array consisting of numbers and variables (with quotes). Example: /min 10 i 30 j /min ["count", 15, 2, "i"]
/mod (number|varname) (number|varname) // Performs a modulo operation of two values and passes the result down the pipe. Can use variable names. Example: /mod i 2
/model [quiet=true|false]?=false (string)? // Sets the model for the current API. Gets the current model name if no argument is provided.
/movingui (string) // activates a movingUI preset by name
/mul (...number|varname|list) // Performs a multiplication of the set of values and passes the result down the pipe. Can use variable names, or a JSON array consisting of numbers and variables (with quotes). Examples: /mul 10 i 30 j /mul ["count", 15, 2, "i"]
/newchat [delete=true|false]?=false // Start a new chat with the current character
/note (string)? // Sets an author's note for the currently selected chat if specified and returns the current note.
/note-depth (number)? // Sets an author's note depth for in-chat positioning if specified and returns the current depth.
/note-frequency (number)? // Sets an author's note insertion frequency if specified and returns the current frequency.
/note-position (before|after|chat)? // Sets an author's note position if specified and returns the current position.
/note-role (system|user|assistant)? // Sets an author's note chat insertion role if specified and returns the current role.
/panels // Toggle UI panels on/off
/parser-flag (STRICT_ESCAPING|REPLACE_GETVAR) (on|off)?=on // Set a parser flag.
/pass (string|number|bool|list|dictionary|closure) // /pass (text) – passes the text to the next command through the pipe. Example: /pass Hello world
/persona-lock [type=chat|character|default]?=chat (string)? // Locks/unlocks a persona (name and avatar) to the current chat. Gets the current lock state for the given type if no state is provided.
/persona-set [mode=lookup|temp|all]?=all (string) // Selects the given persona with its name and avatar (by name or avatar url). If no matching persona exists, applies a temporary name.
/persona-sync // Syncs the user persona in user-attributed messages in the current chat.
/pick-icon // Opens a popup with all the available Font Awesome icons and returns the selected icon's name. Example: /pick-icon | /if left={{pipe}} rule=eq right=false else={: /echo chosen icon: "{{pipe}}" :} {: /echo cancelled icon selection :} |
/popup [scroll=true|false]?=true [large=true|false]?=false [wide=true|false]?=false [wider=true|false]?=false [transparent=true|false]?=false [okButton=string]?=OK [cancelButton=string]? [result=true|false]?=false (string) // Shows a blocking popup with the specified text and buttons. Returns the popup text. Example: /popup large=on wide=on okButton="Confirm" Please confirm this action. /popup okButton="Left" cancelButton="Right" result=true Do you want to go left or right? | /echo 0 means right, 1 means left. Choice: {{pipe}}
/pow (number|varname) (number|varname) // Performs a power operation of two values and passes the result down the pipe. Can use variable names. Example: /pow i 2
/preset (string)? // Sets a preset by name for the current API. Gets the current preset if no name is provided. Example: /preset myPreset /preset
/profile [await=true|false]?=true (string)? // Switch to a connection profile or return the name of the current profile in no argument is provided. Use <None> to switch to no profile.
/profile-create (string) // Create a new connection profile using the current settings.
/profile-get (string)? // Get the details of the connection profile. Returns the selected profile if no argument is provided.
/profile-list // List all connection profile names.
/profile-update // Update the selected connection profile.
/prompt-post-processing (string) // Sets a "Prompt Post-Processing" type. Gets the current selection if no value is provided. Examples: /prompt-post-processing | /echo /prompt-post-processing single
/proxy (string) // Sets a proxy preset by name.
/qr (number) // Activates the specified Quick Reply
/qr-arg (string) (string|number|bool|list|dictionary) // Set a fallback value for a Quick Reply argument. Example: /qr-arg x foo | /echo {{arg::x}}
/qr-chat-set [visible=true|false]?=true (string) // Toggle chat QR set
/qr-chat-set-off (string) // Deactivate chat QR set
/qr-chat-set-on [visible=true|false]?=true (string) // Activate chat QR set
/qr-contextadd [set=string] [label=string]? [id=number]? [chain=true|false]?=false (string) // Add a context menu preset to a QR. If id and label are both provided, id will be used. Example: /qr-contextadd set=MyQRSetWithTheButton label=MyButton chain=true MyQRSetWithContextItems
/qr-contextclear [set=string] [id=number]? (string)? // Remove all context menu presets from a QR. If id and a label are both provided, id will be used. Example: /qr-contextclear set=MyPreset MyButton
/qr-contextdel [set=string] [label=string]? [id=number]? (string) // Remove context menu preset from a QR. If id and label are both provided, id will be used. Example: /qr-contextdel set=MyPreset label=MyButton MyOtherPreset
/qr-create [set=string] [label=string]? [icon=string]? [showlabel=true|false]? [hidden=true|false]?=false [startup=true|false]?=false [user=true|false]?=false [bot=true|false]?=false [load=true|false]?=false [new=true|false]?=false [group=true|false]?=false [title=string]? (string) // Creates a new Quick Reply. Example: /qr-create set=MyPreset label=MyButton /echo 123
/qr-delete [set=string] [label=string]? [id=number]? (string)? // Deletes a Quick Reply from the specified set. (Label must be provided via named or unnamed argument)
/qr-get [set=string] [label=string]? [id=number]? // Get a Quick Reply's properties. Examples: /qr-get set=MyPreset label=MyButton | /echo /qr-get set=MyPreset id=42 | /echo
/qr-list (string) // Gets a list of the names of all quick replies in this quick reply set.
/qr-set [visible=true|false]?=true (string) // Toggle global QR set
/qr-set-create [nosend=true|false]? [before=true|false]? [inject=true|false]? (string) // Create a new preset (overrides existing ones). Example: /qr-set-add MyNewPreset
/qr-set-delete (string) // Delete an existing preset. Example: /qr-set-delete MyPreset
/qr-set-list (all|global|chat)?=all // Gets a list of the names of all quick reply sets.
/qr-set-off (string) // Deactivate global QR set
/qr-set-on [visible=true|false]?=true (string) // Activate global QR set
/qr-set-update [nosend=true|false]? [before=true|false]? [inject=true|false]? (string) // Update an existing preset. Example: /qr-set-update enabled=false MyPreset
/qr-update [newlabel=string]? [id=number]? [set=string] [label=string]? [icon=string]? [showlabel=true|false]? [hidden=true|false]?=false [startup=true|false]?=false [user=true|false]?=false [bot=true|false]?=false [load=true|false]?=false [new=true|false]?=false [group=true|false]?=false [title=string]? (string)? // Updates Quick Reply. Example: /qr-update set=MyPreset label=MyButton newlabel=MyRenamedButton /echo 123
/qrset // DEPRECATED – The command /qrset has been deprecated. Use /qr-set, /qr-set-on, and /qr-set-off instead.
/rand [from=number]?=0 [to=number]?=1 [round=round|ceil|floor]? // Returns a random number between from and to (inclusive). Examples: /rand Returns a random number between 0 and 1. /rand 10 Returns a random number between 0 and 10. /rand from=5 to=10 Returns a random number between 5 and 10.
/random (string)? // Start a new chat with a random character. If an argument is provided, only considers characters that have the specified tag.
/reasoning-get (number)? // Get the contents of a reasoning block of a message. Returns an empty string if the message does not have a reasoning block.
/reasoning-parse [regex=true|false]?=true [return=reasoning|content]?=reasoning [strict=true|false]?=true (string)? // Extracts the reasoning block from a string using the Reasoning Formatting settings.
/reasoning-set [at=number]? [collapse=true|false]? (string)? // 设置消息的推理块。返回推理块内容。
/reasoning-template [quiet=true|false]?=false (string)? // Selects a reasoning template by name, using fuzzy search to find the closest match. Gets the current template if no name is provided. Example: /reasoning-template DeepSeek
/regex [name=string] (string)? // Runs a Regex extension script by name on the provided string. The script must be enabled.
/regex-toggle [state=on|off|toggle]?=toggle [quiet=true|false]?=false (string) // Toggles the state of a specified regex script. Example: /regex-toggle MyScript /regex-toggle state=off Character-specific Script
/reload-page // Reloads the current page. All further commands will not be processed.
/rename-char [silent=true|false]?=true [chats=true|false]?=<null> (string) // Renames the current character.
/renamechat (string) // Renames the current chat.
/replace [mode=literal|regex]?=literal [pattern=string] [replacer=string]? (string) // Replaces text within the provided string based on the pattern. If mode is literal (or omitted), pattern is a literal search string (case-sensitive). If mode is regex, pattern is parsed as an ECMAScript Regular Expression. The replacer replaces based on the pattern in the input text. If replacer is omitted, the replacement(s) will be an empty string. Example: /let x Blue house and blue car || /replace pattern="blue" {{var::x}}                                | /echo  |/# Blue house and  car     || /replace pattern="blue" replacer="red" {{var::x}}                 | /echo  |/# Blue house and red car  || /replace mode=regex pattern="/blue/i" replacer="red" {{var::x}}   | /echo  |/# red house and blue car  || /replace mode=regex pattern="/blue/gi" replacer="red" {{var::x}}  | /echo  |/# red house and red car   ||
/resetpanels // resets UI panels to original state
/round (number|varname) // Rounds a value and passes the result down the pipe. Can use variable names. Example: /round i
/run [...args=string|number|bool|list|dictionary]? (varname|string|closure) // Runs a closure from a scoped variable, or a Quick Reply with the specified name from a currently active preset or from another preset. Named arguments can be referenced in a QR with {{arg::key}}.
/secret-delete [quiet=true|false]?=false [key=string]? (string) // Deletes a secret key by ID.
/secret-id [quiet=true|false]?=false [key=string]? (string) // Sets the ID of a currently active secret key. Gets the ID of the secret key if no value is provided.
/secret-read [quiet=true|false]?=false [key=string]? (string) // Reads a secret key by ID. If key exposure is disabled, this command will not work!
/secret-rename [quiet=true|false]?=false [key=string]? [id=string] (string) // Renames a secret key by ID.
/secret-write [quiet=true|false]?=false [key=string]? [label=string]? (string) // Writes a secret key with a value and an optional label.
/send [compact=true|false]?=false [at=number]? [name=string]?={{user}} [return=pipe|object|toast-html|toast-text|console|none]?=none [raw=true|false]?=true (string) // Adds a user message to the chat log without triggering a generation. If compact is set to true, the message is sent using a compact layout. If name is set, it will be displayed as the message sender. Can be an empty for no name. Example: /send Hello there! /send compact=true Hi
/sendas [name=string] [avatar=string]? [compact=true|false]?=false [at=number]? [return=pipe|object|toast-html|toast-text|console|none]?=none [raw=true|false]?=true (string) // Sends a message as a specific character. Uses the character avatar if it exists in the characters list. Example: /sendas name="Chloe" Hello, guys! will send "Hello, guys!" from "Chloe". /sendas name="Chloe" avatar="BigBadBoss" Hehehe, I am the big bad evil, fear me. will send a message as the character "Chloe", but utilizing the avatar from a character named "BigBadBoss". If "compact" is set to true, the message is sent using a compact layout.
/setentryfield [file=string] [uid=string] [field=key|keysecondary|comment|content|constant|vectorized|selective|selectiveLogic|addMemo|order|position|disable|excludeRecursion|preventRecursion|matchPersonaDescription|matchCharacterDescription|matchCharacterPersonality|matchCharacterDepthPrompt|matchScenario|matchCreatorNotes|delayUntilRecursion|probability|useProbability|depth|group|groupOverride|groupWeight|scanDepth|caseSensitive|matchWholeWords|useGroupScoring|automationId|role|sticky|cooldown|delay|characterFilterNames|characterFilterTags|characterFilterExclude|triggers]?=content (string) // Set a field value (default: content) of the record with the UID from the specified book. To set multiple values for key fields, use comma-delimited list as a value. Example: /setentryfield file=chatLore uid=123 field=key Shadowfang,sword,weapon
/setglobalvar [key=varname] [index=number|string]? [as=string]?=string (string|number|bool|list|dictionary) // Set a global variable value and pass it down the pipe. The index argument is optional. To convert the value to a specific JSON type when using index, use the as argument. Example: /setglobalvar key=color green /setglobalvar key=ages index=John as=number 21
/setinput (string) // Sets the user input to the specified text and passes it to the next command through the pipe. Example: /setinput Hello world
/setpromptentry [...identifier=string|list]? [...name=string|list]? (on|off|toggle)=toggle // Sets the specified prompt manager entry/entries on or off.
/setvar [key=varname] [index=number|string]? [as=string]?=string (string|number|bool|list|dictionary) // Set a local variable value and pass it down the pipe. The index argument is optional. To convert the value to a specific JSON type when using index, use the as argument. Example: /setvar key=color green /setvar key=ages index=John as=number 21
/show-gallery // Shows the gallery.
/sin (number|varname) // Performs a sine operation of a value and passes the result down the pipe. Can use variable names. Example: /sin i
/single // Sets the message style to single document mode without names or avatars visible.
/sort [keysort=true|false]?=true (string|number|list|dictionary) // Sorts a list or dictionary in ascending order and passes the result down the pipe. For lists, returns the list sorted by value. For dictionaries, returns the ordered list of keys after sorting. Setting keysort=false means keys are sorted by associated value. Examples: /sort [5,3,4,1,2] | /echo /sort keysort=false {"a": 1, "d": 3, "c": 2, "b": 5} | /echo
/speak [voice=string]? (string) // Narrate any text using currently selected character's voice. Use voice="Character Name" argument to set other voice from the voice map. Example: /speak voice="Donald Duck" Quack!
/sqrt (number|varname) // Performs a square root operation of a value and passes the result down the pipe. Can use variable names. Example: /sqrt i
/start-reply-with [force=true|false]?=false (string)? // Sets a "Start Reply With". Gets the current value if no value is provided. Use a "force" argument to force set an empty value. Examples: Set the field value: /start-reply-with Sure! Force set an empty value: /start-reply-with force="true" {{noop}}
/stop // Stops the generation and any streaming if it is currently running. Note: This command cannot be executed from the chat input, as sending any message or script from there is blocked during generation. But it can be executed via automations or QR scripts/buttons.
/stop-strings [force=true|false]?=false (list)? // Sets a list of custom stopping strings. Gets the list if no value is provided. Use a "force" argument to force set an empty value. Examples: Force set an empty value: /stop-strings force="true" {{noop}} Value must be a JSON-serialized array: /stop-strings ["goodbye", "farewell"] Pipe characters must be escaped with a backslash: /stop-strings ["left\|right"]
/sub (...number|varname|list) // Performs a subtraction of the set of values and passes the result down the pipe. Can use variable names, or a JSON array consisting of numbers and variables (with quotes). Example: /sub i 5 /sub ["count", 4, "i"]
/substr [start=number]? [end=number]? (string) // Extracts text from the provided string. If start is omitted, it's treated as 0. If start < 0, the index is counted from the end of the string. If start >= the string's length, an empty string is returned. If end is omitted, or if end >= the string's length, extracts to the end of the string. If end < 0, the index is counted from the end of the string. If end <= start after normalizing negative values, an empty string is returned. Example: /let x The morning is upon us.     || /substr start=-3 {{var::x}}         | /echo  |/# us.                    || /substr start=-3 end=-1 {{var::x}}  | /echo  |/# us                     || /substr end=-1 {{var::x}}           | /echo  |/# The morning is upon us || /substr start=4 end=-1 {{var::x}}   | /echo  |/# morning is upon us     ||
/summarize [source=extras|main|webllm]? [prompt=string]? [quiet=true|false]?=false (string)? // Summarizes the given text. If no text is provided, the current chat will be summarized. Can specify the source and the prompt to use.
/sys [compact=true|false]?=false [at=number]? [name=string]? [return=pipe|object|toast-html|toast-text|console|none]?=none [raw=true|false]?=true (string) // Sends a message as a system narrator. If compact is set to true, the message is sent using a compact layout. Example: /sys The sun sets in the west. /sys compact=true A brief note.
/sysgen (string) // Generates a system message using a specified prompt.
/sysname (string)? // Sets a name for future system narrator messages in this chat (display only). Default: System. Leave empty to reset.
/sysprompt [quiet=true|false]?=false [forceGet=true|false]?=false (string)? // Selects a system prompt by name, using fuzzy search to find the closest match. Gets the current system prompt if no name is provided and sysprompt is enabled or forceGet=true is passed. Example: /sysprompt
/sysprompt-off // Disables system prompt
/sysprompt-on // Enables system prompt.
/sysprompt-state (true|false)? // Gets the current system prompt state. If an argument is provided, it will set the system prompt state.
/tag-add [name=string]?={{char}} (string) // Adds a tag to the character. If no character is provided, it adds it to the current character ({{char}}). If the tag doesn't exist, it is created. Example: /tag-add name="Chloe" scenario will add the tag "scenario" to the character named Chloe.
/tag-exists [name=string]?={{char}} (string) // Checks whether the given tag is assigned to the character. If no character is provided, it checks the current character ({{char}}). Example: /tag-exists name="Chloe" scenario will return true if the character named Chloe has the tag "scenario".
/tag-list [name=string]?={{char}} // Lists all assigned tags of the character. If no character is provided, it uses the current character ({{char}}). Note that there is no special handling for tags containing commas, they will be printed as-is. Example: /tag-list name="Chloe" could return something like OC, scenario, edited, funny
/tag-remove [name=string]?={{char}} (string) // Removes a tag from the character. If no character is provided, it removes it from the current character ({{char}}). Example: /tag-remove name="Chloe" scenario will remove the tag "scenario" from the character named Chloe.
/tempchat // Opens a temporary chat with Assistant.
/test [pattern=string] (string) // Tests text for a regular expression match. Returns true if the match is found, false otherwise. Example: /let x Blue house and green car                         || /test pattern="green" {{var::x}}    | /echo  |/# true   || /test pattern="blue" {{var::x}}     | /echo  |/# false  || /test pattern="/blue/i" {{var::x}}  | /echo  |/# true   ||
/theme (string)? // Sets a UI theme by name. If no theme name is is provided, this will return the currently active theme. Example: /theme Cappuccino /theme
/times [guard=on|off]? (number) (closure|subcommand) // Execute any valid slash command enclosed in quotes repeats number of times. Examples: /setvar key=i 1 | /times 5 "/addvar key=i 1" adds 1 to the value of "i" 5 times. /times 4 "/echo {{timesIndex}}" echos the numbers 0 through 4. {{timesIndex}} is replaced with the iteration number (zero-based). Loops are limited to 100 iterations by default, pass guard=off to disable.
/tokenizer (best_match|none|gpt2|llama|llama3|gemma|jamba|qwen2|command_r|command_a|nerd|nerd2|mistral|nemo|yi|claude|deepseek|api_current)? // Selects tokenizer by name. Gets the current tokenizer if no name is provided. Available tokenizers: best_match, none, gpt2, llama, llama3, gemma, jamba, qwen2, command_r, command_a, nerd, nerd2, mistral, nemo, yi, claude, deepseek, api_current
/tokens (string) // Counts the number of tokens in the provided text.
/tools-invoke [parameters=dictionary] (string) // Invokes a registered tool by name. The parameters argument MUST be a JSON-serialized object.
/tools-list [return=pipe|object|toast-html|toast-text|console|none]?=none // Gets a list of all registered tools in the OpenAI function JSON format. Use the return argument to specify the return value type.
/tools-register [name=string] [description=string] [parameters=dictionary] [displayName=string]? [formatMessage=closure] [shouldRegister=closure]? [stealth=true|false]?=false (closure) // Registers a new tool with the tool registry. The parameters argument MUST be a JSON-serialized object with a valid JSON schema. The unnamed argument MUST be a closure that accepts the function parameters as local script variables. See json-schema.org and OpenAI Function Calling for more information. Example: /let key=echoSchema { "$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": { "message": { "type": "string", "description": "The message to echo." } }, "required": [ "message" ] } || /tools-register name=Echo description="Echoes a message. Call when the user is asking to repeat something" parameters={{var::echoSchema}} {: /echo {{var::arg.message}} :}
/tools-unregister (string) // Unregisters a tool from the tool registry.
/translate [target=af|sq|am|ar|hy|az|eu|be|bn|bs|bg|ca|ceb|zh-CN|zh-TW|co|hr|cs|da|nl|en|eo|et|fi|fr|fy|gl|ka|de|el|gu|ht|ha|haw|iw|hi|hmn|hu|is|ig|id|ga|it|ja|jw|kn|kk|km|ko|ku|ky|lo|la|lv|lt|lb|mk|mg|ms|ml|mt|mi|mr|mn|my|ne|no|ny|ps|fa|pl|pt-PT|pt-BR|pa|ro|ru|sm|gd|sr|st|sn|sd|si|sk|sl|so|es|su|sw|sv|tl|tg|ta|te|th|tr|uk|ur|uz|vi|cy|xh|yi|yo|zu]? [provider=string]? (string) // Translate text to a target language. If target language is not provided, the value from the extension settings will be used.
/trigger [await=true|false]?=false (number|string)? // Triggers a message generation. If in group, can trigger a message for the specified group member index or name. If await=true named argument is passed, the command will await for the triggered generation before continuing.
/trimend (string) // Trims the text to the end of the last full sentence.
/trimstart (string) // Trims the text to the start of the first full sentence. Example: /trimstart This is a sentence. And here is another sentence.
/trimtokens [limit=number] [direction=start|end] (string)? // Trims the start or end of text to the specified number of tokens. Example: /trimtokens limit=5 direction=start This is a long sentence with many words
/unhide [name=string]? (number|range)? // Unhides a message from the prompt.
/unlockbg // Unlocks a background for the currently selected chat
/upper (string) // Converts the provided string to uppercase.
/var [key=varname]? [index=number]? [as=string]?=string (varname)? (string|number|bool|list|dictionary|closure)? // Get or set a variable. Use index to access elements of a JSON-serialized list or dictionary. To convert the value to a specific JSON type when using with index, use the as argument. Examples: /let x foo | /var x foo bar | /var x | /echo /let x foo | /var key=x foo bar | /var x | /echo /let x {} | /var index=cool as=number x 1337 | /echo {{var::x}}
/vn // Swaps Visual Novel Mode On/Off
/while [left=varname|string|number] [right=varname|string|number]? [rule=eq|neq|in|nin|gt|gte|lt|lte|not]?=eq [guard=on|off]?=off (closure|subcommand) // Compares the value of the left operand a with the value of the right operand b, and if the condition yields true, then execute any valid slash command enclosed in quotes. Numeric values and string literals for left and right operands supported. Available rules: eq => a == b (strings & numbers) neq => a !== b (strings & numbers) in => a includes b (strings & numbers as strings) nin => a not includes b (strings & numbers as strings) gt => a > b (numbers) gte => a >= b (numbers) lt => a < b (numbers) lte => a <= b (numbers) not => !a (truthy) Examples: /setvar key=i 0 | /while left=i right=10 rule=lte "/addvar key=i 1" adds 1 to the value of "i" until it reaches 10. /while left={{getvar::currentword}} {: /setvar key=currentword {: /do-something-and-return :}() | /echo The current work is "{{getvar::currentword}}" :} executes the defined subcommand as long as the "currentword" variable is truthy (has any content that is not false/empty) Loops are limited to 100 iterations by default, pass guard=off to disable.
/wi-get-timed-effect [file=string] [effect=string] [format=bool|number]?=bool (string) // Get the current state of the timed effect for the record with the UID from the specified book. Example: /wi-get-timed-effect file=chatLore format=bool effect=sticky 123 - returns true or false if the effect is active or not /wi-get-timed-effect file=chatLore format=number effect=sticky 123 - returns the remaining duration of the effect, or 0 if inactive
/wi-set-timed-effect [file=string] [uid=string] [effect=string] (on|off|toggle) // Set a timed effect for the record with the UID from the specified book. The duration must be set in the entry itself. Will only be applied for the current chat. Enabling an effect that was already active refreshes the duration. If the last chat message is swiped or deleted, the effect will be removed. Example: /wi-set-timed-effect file=chatLore uid=123 effect=sticky on
/world [state=on|off|toggle]? [silent=true|false]? (string)? // Sets active World, or unsets if no args provided, use state=off and state=toggle to deactivate or toggle a World, use silent=true to suppress toast messages.
/yt-script [lang=aa|ab|ae|af|ak|am|an|ar|as|av|ay|az|ba|be|bg|bh|bi|bm|bn|bo|br|bs|ca|ce|ch|co|cr|cs|cu|cv|cy|da|de|dv|dz|ee|el|en|eo|es|et|eu|fa|ff|fi|fj|fo|fr|fy|ga|gd|gl|gn|gu|gv|ha|he|hi|ho|hr|ht|hu|hy|hz|ia|id|ie|ig|ii|ik|io|is|it|iu|ja|jv|ka|kg|ki|kj|kk|kl|km|kn|ko|kr|ks|ku|kv|kw|ky|la|lb|lg|li|ln|lo|lt|lu|lv|mg|mh|mi|mk|ml|mn|mr|ms|mt|my|na|nb|nd|ne|ng|nl|nn|no|nr|nv|ny|oc|oj|om|or|os|pa|pi|pl|ps|pt|qu|rm|rn|ro|ru|rw|sa|sc|sd|se|sg|si|sk|sl|sm|sn|so|sq|sr|ss|st|su|sv|sw|ta|te|tg|th|ti|tk|tl|tn|to|tr|ts|tt|tw|ty|ug|uk|ur|uz|ve|vi|vo|wa|wo|xh|yi|yo|za|zh|zu]? (string) // Scrape a transcript from a YouTube video by ID or URL.