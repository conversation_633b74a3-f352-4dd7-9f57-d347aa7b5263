---
type: "always_apply"
---

概述：
- 我们正在进行JavaScript脚本开发，用于在SillyTavern程序中，结合“酒馆助手 (TavernHelpe)” Extension，自动化地控制用户与LLM对话的方方面面。
- “酒馆助手“ Extension是SillyTavern的拓展程序，用于在SillyTavern中运行JavaScript脚本。其提供了JavaScript运行环境和若干第三方库，而且提供了许多函数（详见"参考资料\@types"）供我们使用。
- “酒馆助手“ Extension提供的JavaScript环境安装了以下库：jquery, jquery-ui, lodash, toastr, yaml等等
- "参考资料"文件夹中的内容非常有用。其中，"参考资料\@types"文件夹是“酒馆助手“ Extension提供的各种函数的参考手册，而"slash_command.txt"文件是SillyTavern程序内置的STscript (a simple yet powerful scripting language that could be used to expand the functionality of SillyTavern)的Reference手册，请充分使用这些资源以提高效率。
- Node.js版本：v22.16.0

---

具体工作方式：
- 我们正在开发的脚本位于"scripts\newScript.js"。
- 由于SillyTavern环境不显示console，因此日志输出没有意义，你无需配置任何日志输出（例如console.log）。
- 由于我们的js脚本最终在SillyTavern环境中运行，你可能无法准确地测试它们，让我手动进行测试即可。